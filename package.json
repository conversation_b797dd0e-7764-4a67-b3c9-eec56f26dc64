{"name": "espirai-site", "version": "1.0.0", "description": "Espirai website with React development environment", "main": "src/static/js/index.tsx", "scripts": {"start": "webpack serve --mode development", "build": "webpack --mode production", "dev": "webpack serve --mode development --open", "type-check": "tsc --noEmit"}, "dependencies": {"@remix-run/router": "^1.3.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.8.0"}, "devDependencies": {"@babel/core": "^7.27.4", "@babel/preset-env": "^7.27.2", "@babel/preset-react": "^7.27.1", "@babel/preset-typescript": "^7.27.1", "@svgr/webpack": "^6.5.1", "@types/react": "^18.0.27", "@types/react-dom": "^18.0.10", "@types/react-router-dom": "^5.3.3", "babel-loader": "^10.0.0", "css-loader": "^6.7.3", "file-loader": "^6.2.0", "html-webpack-plugin": "^5.5.0", "style-loader": "^3.3.1", "ts-loader": "^9.4.2", "typescript": "^4.9.5", "webpack": "^5.75.0", "webpack-cli": "^5.0.1", "webpack-dev-server": "^4.7.4"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}