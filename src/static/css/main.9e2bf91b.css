body {
    font-size: calc(max(3.1415926535vmin, 16px));
    font-weight: 200;
    height: 100dvh;
    line-height: 133%
}

@media (max-width: 600px) {
    body {
        font-weight:300
    }
}

button {
    font-family: inherit;
    font-size: inherit;
    font-weight: inherit;
    line-height: 133%
}

@font-face {
    font-family: Monaspace Krypton;
    font-weight: 1 999;
    src: url(/static/media/MonaspaceKryptonVarVF[wght,wdth,slnt].8e130f9ffb14c10d7611.woff2) format("woff2")
}

@font-face {
    font-family: Monaspace Neon;
    font-weight: 1 999;
    src: url(/static/media/MonaspaceNeonVarVF[wght,wdth,slnt].f7de8168e3e86ea8ea06.woff2) format("woff2")
}

@font-face {
    font-family: Monaspace Xenon;
    font-weight: 1 999;
    src: url(/static/media/MonaspaceXenonVarVF[wght,wdth,slnt].fcb72ae97ba694531f47.woff2) format("woff2")
}

body {
    margin: 0;
    overflow-x: hidden
}

#root {
    min-height: 100dvh
}

.circle-container {
    bottom: 0;
    left: 0;
    overflow: hidden;
    position: fixed;
    right: 0;
    top: 0;
    z-index: -1
}

.prompt-container {
    align-items: center;
    display: flex;
    flex-direction: column;
    height: 100dvh;
    justify-content: center;
    left: 0;
    position: absolute;
    top: 0;
    width: 100vw;
    z-index: 10
}

.ztop {
    display: block;
    z-index: 10
}

.circle {
    border-radius: 50%;
    filter: blur(80px);
    height: 100vmax;
    left: 40%;
    mix-blend-mode: screen;
    position: absolute;
    top: -80vmax;
    width: 100vmax
}

#circle1 {
    background-color: #ebdf8799;
    background-color: color(display-p3 .92 .87 0/.5)
}

#circle2 {
    background-color: #00c8ff99;
    background-color: color(display-p3 0 .78 1/.6)
}

#circle3 {
    background-color: #feb6ff99;
    background-color: color(display-p3 1 .71 1/.6)
}

@keyframes orbit {
    0% {
        transform: rotate(0deg) translateX(20vmin) rotate(0deg)
    }

    to {
        transform: rotate(1turn) translateX(20vmin) rotate(-1turn)
    }
}

@keyframes rorbit {
    to {
        transform: rotate(0deg) translateX(20vmin) rotate(0deg)
    }

    0% {
        transform: rotate(1turn) translateX(20vmin) rotate(-1turn)
    }
}

@keyframes cursor-blink {
    0% {
        opacity: 1
    }

    50% {
        opacity: 1
    }

    66% {
        opacity: 0
    }

    to {
        opacity: 0
    }
}

.cursor {
    animation: cursor-blink 1.5s infinite
}

#circle1 {
    animation: orbit 6s linear infinite
}

#circle2 {
    animation: rorbit 10s linear infinite
}

#circle3 {
    animation: rorbit 8s linear infinite
}

@keyframes blear {
    0% {
        filter: blur(.1vmin);
        opacity: .2;
        transform: scale(1)
    }

    80% {
        filter: blur(.1vmin);
        opacity: .9;
        transform: scale(1)
    }

    90% {
        filter: blur(.5vmin);
        opacity: .6;
        transform: scale(1.01)
    }

    to {
        filter: blur(.1vmin);
        opacity: .2;
        transform: scale(1)
    }
}

body {
    background-color: var(--background-color);
    color: var(--text-color)
}

* {
    --vermillion: #cd523d;
    --text-color: hsl(var(--hue),100%,8%);
    --dim-text-color: hsla(var(--hue),100%,10%,40%);
    --background-color: hsl(var(--hue),10%,98%);
    --link-color: #3d66cd;
    --link-hover-color: #214dbb;
    --button-color: var(--vermillion);
    --button-hover-color: var(--vermillion)
}

#terminal {
    mix-blend-mode: multiply
}

@media (prefers-color-scheme: dark) {
    * {
        --text-color:#fff;
        --dim-text-color: #ffffff88;
        --background-color: #16161d;
        --dim-text-color: hsla(var(--hue),100%,90%,50%);
        --vermillion: #ff735a;
        --link-color: #83a6ff;
        --link-hover-color: #bdd0ff;
        --button-color: var(--vermillion);
        --button-hover-color: var(--vermillion)
    }

    @supports (color: hsl(0 0% 0%/0)) {
        * {
            --text-color:hsl(var(--hue),100%,94%);
            --background-color: hsl(var(--hue),10%,10%)
        }
    }

    #terminal {
        mix-blend-mode: screen
    }

    #circle1 {
        background-color: #ebdf8733;
        background-color: color(display-p3 .92 .87 0/.2)
    }

    #circle2 {
        background-color: #00c8ff33;
        background-color: color(display-p3 0 .78 1/.2)
    }

    #circle3 {
        background-color: #feb6ff33;
        background-color: color(display-p3 1 .71 1/.2)
    }
}

.cursor {
    font-weight: 700
}

@keyframes blink {
    50% {
        opacity: 0
    }
}

#geist {
    stroke-width: 2;
    cursor: pointer;
    display: inline-block;
    height: 1.5em;
    transform: translateY(-.19em);
    vertical-align: bottom;
    width: 1.5em
}

#geist.waiting path {
    stroke: var(--vermillion);
    fill: none;
    animation: cursor-pulse 1s infinite
}

#geist.outputting,#geist:hover {
    stroke: var(--vermillion);
    path {
        fill: var(--vermillion)
    }
}

#geist.autocompleting {
    stroke: var(--vermillion);
    path {
        fill: var(--vermillion)
    }
}

#geist.inputting {
    stroke: var(--dim-text-color)
}

@keyframes cursor-pulse {
    0% {
        opacity: 1
    }

    50% {
        opacity: .5
    }

    to {
        opacity: 1
    }
}

.hoverLink,a,a:visited {
    color: var(--link-color);
    cursor: pointer;
    text-decoration: none
}

.hoverLink:hover,a:hover {
    color: var(--link-hover-color);
    text-decoration: underline
}

.old-prompt {
    color: var(--dim-text-color)
}

:hover>.noHover,:not(:hover)>.onHover {
    display: none
}

button {
    background: none;
    border: none;
    color: var(--button-color);
    cursor: pointer;
    padding: 0
}

button:hover,span.cwd:hover {
    color: var(--button-hover-color)
}

button:hover:before {
    content: ">";
    float: left;
    margin-left: -.75em
}

.fullname {
    color: var(--dim-text-color)
}

.typedLine {
    animation: reveal .2s linear forwards;
    -webkit-mask-position: 0;
    mask-position: 0;
    -webkit-mask-size: 200%;
    mask-size: 200%;
    max-width: 34em;
    white-space: pre-wrap
}

@keyframes reveal {
    0% {
        -webkit-mask-position: 100% 100%;
        mask-position: 100% 100%
    }

    to {
        -webkit-mask-position: 0 0;
        mask-position: 0 0
    }
}

.username {
    display: inline-block;
    min-width: 8em;
    text-align: left
}

.terminal {
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
    min-height: 100dvh;
    transform-origin: bottom left;
    transition: transform 1.15s cubic-bezier(.5,0,.82,1) .15s
}

.terminal p {
    margin-top: 0
}

.terminal p.old-prompt {
    margin-top: 1em
}

body.loading .terminal {
    transform: scale(1)
}

#prompt {
    cursor: pointer;
    margin-top: 1em
}

.inputText {
    white-space: pre-wrap
}

.directory {
    color: var(--text-color)
}

.hoverText {
    color: var(--dim-text-color)
}

.inputText {
    background: #0000;
    border: none;
    border-radius: 0;
    caret-color: #0000;
    color: var(--text-color);
    font-family: inherit;
    font-size: inherit;
    font-weight: inherit;
    max-width: 100%;
    padding: 0 0 0 1ch
}

.inputText:focus {
    outline: none
}

.inputText::hover {
    border-bottom: 1px solid var(--dim-text-color)
}

.role {
    display: inline-block;
    min-width: 4em;
    text-align: left
}

body {
    font-family: Monaspace Neon,monospace
}

h1 {
    font-size: calc(max(5vmin, 16pt));
    font-weight: 200
}

/*# sourceMappingURL=main.9e2bf91b.css.map*/
