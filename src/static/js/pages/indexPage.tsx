import React from 'react';

interface IndexPageProps {
  expand?: string;
}

const IndexPage: React.FC<IndexPageProps> = ({ expand }) => {
  return (
    <div style={{ padding: '20px', fontFamily: 'monospace', backgroundColor: '#000', color: '#00ff00', minHeight: '100vh' }}>
      <h1 style={{ color: '#00ff00', textAlign: 'center', fontSize: '2em' }}>
        🚀 Espirai - AI Development Platform
      </h1>
      
      <div style={{ border: '2px solid #00ff00', padding: '20px', marginTop: '20px' }}>
        <h2>✅ Development Environment Status</h2>
        <ul style={{ listStyle: 'none', padding: 0 }}>
          <li>✅ React is working!</li>
          <li>✅ TypeScript compilation successful</li>
          <li>✅ Hot reloading enabled</li>
          <li>✅ Webpack dev server running on localhost:3000</li>
          <li>✅ Routing configured</li>
        </ul>
        
        {expand && (
          <div style={{ marginTop: '20px', padding: '10px', border: '1px solid #00ff00' }}>
            <p>📋 Route parameter expand: <strong>{expand}</strong></p>
          </div>
        )}
        
        <div style={{ marginTop: '30px' }}>
          <h3>🎯 Available Routes:</h3>
          <ul style={{ listStyle: 'none', padding: 0 }}>
            <li>• <a href="/" style={{ color: '#00ff00' }}>/ (home)</a></li>
            <li>• <a href="/about" style={{ color: '#00ff00' }}>/about</a></li>
            <li>• <a href="/jobs" style={{ color: '#00ff00' }}>/jobs</a></li>
            <li>• <a href="/who" style={{ color: '#00ff00' }}>/who</a></li>
            <li>• <a href="/support" style={{ color: '#00ff00' }}>/support</a></li>
          </ul>
        </div>
        
        <div style={{ marginTop: '30px', textAlign: 'center' }}>
          <p style={{ fontSize: '1.2em' }}>🎉 Your React development environment is ready!</p>
        </div>
      </div>
    </div>
  );
};

export default IndexPage;
