import React from 'react';
import Terminal from '../components/terminal';
import '../../css/pages/indexpage.css';

interface IndexPageProps {
  expand?: string;
}

const IndexPage: React.FC<IndexPageProps> = ({ expand }) => {
  return (
    <div className="index-page">
      <Terminal
        prompt="/dev/agents >"
        onInteraction={() => {}}
        expand={expand}
      />
    </div>
  );
};

export default IndexPage;
