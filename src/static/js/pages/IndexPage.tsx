import React from "react";

const IndexPage = () => {
  return (
    <div style={{
      backgroundColor: "#000",
      color: "#00ff00",
      fontFamily: "Monaco, Menlo, monospace",
      fontSize: "14px",
      minHeight: "100vh",
      padding: "20px"
    }}>
      <h1 style={{ fontSize: "16px", margin: "0 0 20px 0" }}>/dev/agents</h1>
      <div style={{ borderBottom: "1px solid #00ff00", marginBottom: "40px" }}></div>
      
      <p style={{ margin: "0 0 40px 0", fontSize: "16px" }}>
        We're building a next-gen operating system for AI agents.
      </p>

      <section style={{ marginBottom: "60px" }}>
        <h2 style={{ fontSize: "16px", margin: "0 0 20px 0" }}>About</h2>
        <div style={{ borderBottom: "1px solid #00ff00", marginBottom: "20px" }}></div>
        <p style={{ margin: "0 0 20px 0", lineHeight: "1.6" }}>
          Modern AI will fundamentally change how people use software in their daily lives.
        </p>
      </section>

      <section style={{ marginBottom: "60px" }}>
        <h2 style={{ fontSize: "16px", margin: "0 0 20px 0" }}>Jobs</h2>
        <div style={{ borderBottom: "1px solid #00ff00", marginBottom: "20px" }}></div>
        <p style={{ margin: "0" }}>
          <a href="mailto:<EMAIL>" style={{ color: "#00ff00" }}>Join us?</a>
        </p>
      </section>

      <section>
        <h2 style={{ fontSize: "16px", margin: "0 0 20px 0" }}>People</h2>
        <div style={{ borderBottom: "1px solid #00ff00", marginBottom: "20px" }}></div>
        <ul style={{ listStyle: "none", padding: 0, margin: 0 }}>
          <li>dps → David Singleton</li>
          <li>ficus → Ficus Kirkpatrick</li>
          <li>hbarra → Hugo Barra</li>
          <li>nj → Nicholas Jitkoff</li>
        </ul>
      </section>
    </div>
  );
};

export default IndexPage;
