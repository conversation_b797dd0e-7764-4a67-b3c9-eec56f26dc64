import React from 'react';

interface IndexPageProps {
  expand?: string;
}

const IndexPage: React.FC<IndexPageProps> = ({ expand }) => {
  return (
    <div style={{ 
      padding: '20px', 
      fontFamily: 'monospace', 
      backgroundColor: '#000', 
      color: '#00ff00', 
      minHeight: '100vh' 
    }}>
      <h1 style={{ 
        color: '#00ff00', 
        textAlign: 'center', 
        fontSize: '2em',
        marginBottom: '30px'
      }}>
        🚀 Espirai - AI Development Platform
      </h1>
      
      <div style={{ 
        border: '2px solid #00ff00', 
        padding: '20px', 
        marginTop: '20px',
        borderRadius: '5px'
      }}>
        <h2 style={{ color: '#00ff00', marginBottom: '20px' }}>
          ✅ Development Environment Status
        </h2>
        
        <ul style={{ listStyle: 'none', padding: 0, lineHeight: '1.8' }}>
          <li>✅ React is working!</li>
          <li>✅ TypeScript compilation successful</li>
          <li>✅ Hot reloading enabled</li>
          <li>✅ Webpack dev server running on localhost:3000</li>
          <li>✅ Routing configured</li>
          <li>✅ No more white screen!</li>
        </ul>
        
        {expand && (
          <div style={{ 
            marginTop: '20px', 
            padding: '10px', 
            border: '1px solid #00ff00',
            borderRadius: '3px'
          }}>
            <p>📋 Route parameter expand: <strong>{expand}</strong></p>
          </div>
        )}
        
        <div style={{ marginTop: '30px' }}>
          <h3 style={{ color: '#00ff00' }}>🎯 Test the Routes:</h3>
          <ul style={{ listStyle: 'none', padding: 0, lineHeight: '1.6' }}>
            <li>• <a href="/" style={{ color: '#00ff00', textDecoration: 'underline' }}>/ (home)</a></li>
            <li>• <a href="/about" style={{ color: '#00ff00', textDecoration: 'underline' }}>/about</a></li>
            <li>• <a href="/jobs" style={{ color: '#00ff00', textDecoration: 'underline' }}>/jobs</a></li>
            <li>• <a href="/who" style={{ color: '#00ff00', textDecoration: 'underline' }}>/who</a></li>
            <li>• <a href="/support" style={{ color: '#00ff00', textDecoration: 'underline' }}>/support</a></li>
          </ul>
        </div>
        
        <div style={{ 
          marginTop: '30px', 
          textAlign: 'center',
          padding: '15px',
          border: '1px dashed #00ff00',
          borderRadius: '5px'
        }}>
          <p style={{ fontSize: '1.2em', margin: 0 }}>
            🎉 Your React development environment is ready!
          </p>
          <p style={{ fontSize: '0.9em', marginTop: '10px', opacity: 0.8 }}>
            The original terminal component can now be enabled
          </p>
        </div>
      </div>
    </div>
  );
};

export default IndexPage;
