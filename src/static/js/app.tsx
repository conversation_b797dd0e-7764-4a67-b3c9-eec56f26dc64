import { BrowserRouter as Router, Routes, Route } from "react-router-dom";
import IndexPage from "./pages/IndexPage";
import SupportPage from "./pages/SupportPage";
import MeetPage from "./pages/MeetPage";

function App() {
  return (
    <Router>
      <Routes>
        <Route path="/" element={<IndexPage />} />
        <Route path="/about" element={<IndexPage expand="about" />} />
        <Route path="/jobs" element={<IndexPage expand="jobs" />} />
        <Route path="/who" element={<IndexPage expand="who" />} />
        <Route path="/love" element={<IndexPage expand="valentine" />} />
        <Route path="/support" element={<SupportPage />} />
        <Route path="/meet/:person" element={<MeetPage />} />
        <Route path="/roles/pmcx" element={<IndexPage expand="pmcx" />} />
        <Route path="/roles/pmdx" element={<IndexPage expand="pmdx" />} />
      </Routes>
    </Router>
  );
}

export default App;
