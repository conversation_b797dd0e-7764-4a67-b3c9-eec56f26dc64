{"name": "@svgr/webpack", "description": "SVGR webpack loader.", "version": "6.5.1", "main": "./dist/index.js", "types": "./dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "repository": "https://github.com/gregberge/svgr/tree/main/packages/webpack", "author": "<PERSON> <<EMAIL>>", "publishConfig": {"access": "public"}, "keywords": ["svgr", "svg", "react", "webpack", "webpack-loader"], "engines": {"node": ">=10"}, "homepage": "https://react-svgr.com", "funding": {"type": "github", "url": "https://github.com/sponsors/gregberge"}, "license": "MIT", "scripts": {"reset": "rm -rf dist", "build": "rollup -c ../../build/rollup.config.js", "prepublishOnly": "npm run reset && npm run build"}, "dependencies": {"@babel/core": "^7.19.6", "@babel/plugin-transform-react-constant-elements": "^7.18.12", "@babel/preset-env": "^7.19.4", "@babel/preset-react": "^7.18.6", "@babel/preset-typescript": "^7.18.6", "@svgr/core": "^6.5.1", "@svgr/plugin-jsx": "^6.5.1", "@svgr/plugin-svgo": "^6.5.1"}, "devDependencies": {"babel-loader": "^9.0.0", "memory-fs": "^0.5.0", "url-loader": "^4.1.1", "webpack": "^5.74.0"}, "gitHead": "d5efedd372999692f84d30072e502b5a6b8fe734"}