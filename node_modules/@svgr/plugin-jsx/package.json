{"name": "@svgr/plugin-jsx", "description": "Transform SVG into JSX", "version": "6.5.1", "main": "./dist/index.js", "types": "./dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "repository": "https://github.com/gregberge/svgr/tree/main/packages/plugin-jsx", "author": "<PERSON> <<EMAIL>>", "publishConfig": {"access": "public"}, "keywords": ["svgr-plugin"], "engines": {"node": ">=10"}, "homepage": "https://react-svgr.com", "funding": {"type": "github", "url": "https://github.com/sponsors/gregberge"}, "license": "MIT", "scripts": {"reset": "rm -rf dist", "build": "rollup -c ../../build/rollup.config.js", "prepublishOnly": "npm run reset && npm run build"}, "peerDependencies": {"@svgr/core": "^6.0.0"}, "dependencies": {"@babel/core": "^7.19.6", "@svgr/babel-preset": "^6.5.1", "@svgr/hast-util-to-babel-ast": "^6.5.1", "svg-parser": "^2.0.4"}, "gitHead": "d5efedd372999692f84d30072e502b5a6b8fe734"}